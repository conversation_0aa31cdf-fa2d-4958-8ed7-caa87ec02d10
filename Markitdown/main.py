import os

# https://github.com/Textualize/rich
from rich.console import Console
from rich.panel import Panel

# https://github.com/microsoft/markitdown
from markitdown import MarkItDown

console = Console()

MARKITDOWN_LOGO = """
    ███╗   ███╗ █████╗ ██████╗ ██╗  ██╗██╗████████╗██████╗  ██████╗ ██╗    ██╗███╗   ██╗
    ████╗ ████║██╔══██╗██╔══██╗██║ ██╔╝██║╚══██╔══╝██╔══██╗██╔═══██╗██║    ██║████╗  ██║
    ██╔████╔██║███████║██████╔╝█████╔╝ ██║   ██║   ██║  ██║██║   ██║██║ █╗ ██║██╔██╗ ██║
    ██║╚██╔╝██║██╔══██║██╔══██╗██╔═██╗ ██║   ██║   ██║  ██║██║   ██║██║███╗██║██║╚██╗██║
    ██║ ╚═╝ ██║██║  ██║██║  ██║██║  ██╗██║   ██║   ██████╔╝╚██████╔╝╚███╔███╔╝██║ ╚████║
    ╚═╝     ╚═╝╚═╝  ╚═╝╚═╝  ╚═╝╚═╝  ╚═╝╚═╝   ╚═╝   ╚═════╝  ╚═════╝  ╚══╝╚══╝ ╚═╝  ╚═══╝
"""


def main():
    try:
        console.print(
            Panel.fit(
                f"[bold cyan]{MARKITDOWN_LOGO}[/bold cyan]\n"
                "[bold blue]📝 Markitdown - Conversion PDF vers Markdown[/bold blue]\n"
                "[dim]Utilisation Markitdown pour l'analyse de documents PDF[/dim]",
                border_style="blue",
            )
        )

        pdf_file_name = "../data/trombinoscope.pdf"
        name_without_suff = ".".join(os.path.basename(pdf_file_name).split(".")[:-1])

        script_dir = os.path.dirname(os.path.abspath(__file__))
        local_md_dir = os.path.join(script_dir, "output", name_without_suff)

        d = MarkItDown(docintel_endpoint="<document_intelligence_endpoint>")
result = md.convert("test.pdf")
print(result.text_content)

    except KeyboardInterrupt:
        return


if __name__ == "__main__":
    main()
