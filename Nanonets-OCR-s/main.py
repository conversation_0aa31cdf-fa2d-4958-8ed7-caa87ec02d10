import os
import glob
import re
import time
from PIL import Image

# https://github.com/Belval/pdf2image
from pdf2image import convert_from_path

# https://github.com/huggingface/transformers
from transformers import AutoTokenizer, AutoProcessor, AutoModelForImageTextToText
from transformers.generation.configuration_utils import GenerationConfig

# https://github.com/Textualize/rich
from rich.console import Console
from rich.progress import (
    Progress,
    SpinnerColumn,
    BarColumn,
    TaskProgressColumn,
    TimeElapsedColumn,
    TextColumn,
)
from rich.panel import Panel

console = Console()

NANONETS_ASCII_ART = r"""
    ███╗   ██╗ █████╗ ███╗   ██╗ ██████╗ ███╗   ██╗███████╗████████╗███████╗
    ████╗  ██║██╔══██╗████╗  ██║██╔═══██╗████╗  ██║██╔════╝╚══██╔══╝██╔════╝
    ██╔██╗ ██║███████║██╔██╗ ██║██║   ██║██╔██╗ ██║█████╗     ██║   ███████╗
    ██║╚██╗██║██╔══██║██║╚██╗██║██║   ██║██║╚██╗██║██╔══╝     ██║   ╚════██║
    ██║ ╚████║██║  ██║██║ ╚████║╚██████╔╝██║ ╚████║███████╗   ██║   ███████║
    ╚═╝  ╚═══╝╚═╝  ╚═╝╚═╝  ╚═══╝ ╚═════╝ ╚═╝  ╚═══╝╚══════╝   ╚═╝   ╚══════╝

                    ██████╗  ██████╗██████╗       ███████╗
                   ██╔═══██╗██╔════╝██╔══██╗      ██╔════╝
                   ██║   ██║██║     ██████╔╝█████╗███████╗
                   ██║   ██║██║     ██╔══██╗╚════╝╚════██║
                   ╚██████╔╝╚██████╗██║  ██║      ███████║
                    ╚═════╝  ╚═════╝╚═╝  ╚═╝      ╚══════╝

                     Conversion PDF vers Markdown avec IA
                     Analyse intelligente de documents PDF
"""

def strip_rich_tags(s):
    return re.sub(r"\[.*?\]", "", s)


def pdf_to_images(pdf_path, local_image_dir):
    images = convert_from_path(pdf_path, dpi=150)
    images = [image.convert("RGB") for image in images]

    for i, image in enumerate(images):
        image_path = os.path.join(local_image_dir, f"page_{i + 1}.png")
        image.save(image_path)


def extract_page_number(filename):
    """Trier les fichiers par ordre numérique des pages"""

    # Extraire le numéro de page du nom de fichier (ex: page_1.png -> 1)
    match = re.search(r"page_(\d+)\.png", os.path.basename(filename))
    return int(match.group(1)) if match else 0


def load_model():
    with console.status(
        "[bold green]Chargement du modèle Nanonets OCR...", spinner="dots"
    ):
        # https://huggingface.co/nanonets/Nanonets-OCR-s
        model_path = "nanonets/Nanonets-OCR-s"
        model = AutoModelForImageTextToText.from_pretrained(
            model_path,
            torch_dtype="auto",
            device_map="auto",
        )
        model.eval()

        processor = AutoProcessor.from_pretrained(model_path, use_fast=True)
        tokenizer = AutoTokenizer.from_pretrained(model_path)

    console.print("✅ [bold green]Modèle chargé avec succès!")
    return model, processor, tokenizer


def ocr_page_with_nanonets_s(image_path, model, processor, max_new_tokens=4096):
    user_prompt = """Extract the text from the above document as if you were reading it naturally. Return the tables in mardown format. Return the equations in LaTeX representation. If there is an image in the document and image caption is not present, add a small description in french of the image inside the <img></img> tag; otherwise, add the image caption inside <img></img>. Watermarks should be wrapped in brackets. Ex: <watermark>OFFICIAL COPY</watermark>. Page numbers should be wrapped in brackets. Ex: <page_number>14</page_number> or <page_number>9/22</page_number>. Prefer using ☐ and ☑ for check boxes."""

    image = Image.open(image_path)
    messages = [
        {"role": "system", "content": "You are a helpful assistant."},
        {
            "role": "user",
            "content": [
                {"type": "image", "image": f"file://{image_path}"},
                {"type": "text", "text": user_prompt},
            ],
        },
    ]
    text = processor.apply_chat_template(
        messages, tokenize=False, add_generation_prompt=True
    )
    inputs = processor(text=[text], images=[image], padding=True, return_tensors="pt")
    inputs = inputs.to(model.device)

    generation_config = GenerationConfig(
        max_new_tokens=max_new_tokens,
        do_sample=False,
        temperature=1e-06,
        repetition_penalty=1.05,
        pad_token_id=model.config.pad_token_id,
        eos_token_id=model.config.eos_token_id,
        bos_token_id=model.config.bos_token_id,
    )

    output_ids = model.generate(
        **inputs,
        generation_config=generation_config
    )
    generated_ids = [
        output_ids[len(input_ids) :]
        for input_ids, output_ids in zip(inputs.input_ids, output_ids)
    ]

    output_text = processor.batch_decode(
        generated_ids, skip_special_tokens=True, clean_up_tokenization_spaces=True
    )

    return output_text


def main():
    try:
        console.print(
            Panel.fit(
                f"[bold cyan]{NANONETS_ASCII_ART}[/bold cyan]\n"
                "[bold blue]📝 Conversion PDF vers Markdown[/bold blue]\n"
                "[dim]Utilisation du modèle 'Nanonets-OCR-s' pour l'analyse de documents PDF[/dim]",
                border_style="blue",
            )
        )

        return
        pdf_path = "../data/trombinoscope.pdf"

        name_without_suff = ".".join(os.path.basename(pdf_path).split(".")[:-1])
        script_dir = os.path.dirname(os.path.abspath(__file__))
        local_md_dir = os.path.join(script_dir, "output", name_without_suff)
        local_image_dir = os.path.join(local_md_dir, "images")

        os.makedirs(local_image_dir, exist_ok=True)

        console.print(f"📁 [cyan]Répertoire de sortie:[/cyan] {local_md_dir}")
        console.print(f"🖼️  [cyan]Répertoire des images:[/cyan] {local_image_dir}")

        # pdf_to_images(pdf_path, local_image_dir)

        model, processor, _ = load_model()

        image_files = glob.glob(os.path.join(local_image_dir, "*.png"))
        if not image_files:
            console.print(
                f"❌ [bold red]Aucune image trouvée dans le répertoire {local_image_dir}[/bold red]"
            )
            return
        image_files.sort(key=extract_page_number)

        console.print(
            f"📊 [bold yellow]Traitement de {len(image_files)} images...[/bold yellow]"
        )

        concatenated_results = ""
        total_processing_time = 0

        with Progress(
            SpinnerColumn(),
            TextColumn("[progress.description]{task.description}"),
            BarColumn(),
            TaskProgressColumn(),
            TimeElapsedColumn(),
            console=console,
        ) as progress:
            task = progress.add_task("🔄 Traitement des images...", total=len(image_files))

            for i, image_path in enumerate(image_files):
                progress.update(
                    task,
                    description=f"🔄 Page {i + 1}/{len(image_files)}: {os.path.basename(image_path)}",
                )

                start_time = time.time()

                result = ocr_page_with_nanonets_s(
                    image_path, model, processor, max_new_tokens=15000
                )

                end_time = time.time()
                processing_time = end_time - start_time
                total_processing_time += processing_time

                console.print(
                    f"⏱️  [dim]Page {i + 1} traitée en {processing_time:.2f}s[/dim]"
                )

                if i > 0:
                    concatenated_results += "\n\n---\n\n"

                concatenated_results += result[0]
                progress.advance(task)

        output_md_path = os.path.join(local_md_dir, f"nanonets_{name_without_suff}.md")
        with open(output_md_path, "w", encoding="utf-8") as f:
            f.write(concatenated_results)

        average_time = total_processing_time / len(image_files) if image_files else 0

        console.print(
            f"💾 [bold green]Résultat sauvegardé:[/bold green] [link]{output_md_path}[/link]"
        )
        console.print(
            f"⏱️  [bold cyan]Temps total de traitement:[/bold cyan] {total_processing_time:.2f}s"
        )
        console.print(
            f"📊 [bold cyan]Temps moyen par image:[/bold cyan] {average_time:.2f}s"
        )

        panel_text = (
            f"[bold green]🎉 Conversion terminée avec succès![/bold green]\n"
            f"📄 Fichier de sortie: [cyan]{os.path.basename(output_md_path)}[/cyan]\n"
            f"⏱️ Temps total: [yellow]{total_processing_time:.2f}s[/yellow] | "
            f"Temps moyen: [yellow]{average_time:.2f}s/image[/yellow]"
        )
        max_length = max(len(strip_rich_tags(line)) for line in panel_text.split("\n"))
        console.print(
            Panel.fit(
                panel_text,
                border_style="green",
                width=max_length
            )
        )
    except KeyboardInterrupt:
        return
    except Exception as e:
        console.print(f"❌ [bold red]Erreur inattendue: {e}[/bold red]")
        return

if __name__ == "__main__":
    main()
